<script lang="ts">
    import MessageComposerBar from '../../components/message-composer/MessageComposerBar.svelte';

    const handleCreateMessage = (event: CustomEvent<string>) => {
        console.log(event.detail);
    }
</script>

<h1>Message composer</h1>

<div class="message-window">
    <div class="content"></div>

    <div class="composer">
        <MessageComposerBar on:create-message={handleCreateMessage}/>
    </div>
</div>

<style lang="less">
    h1 {
        width: 100%;
        text-align: center;
    }

    .message-window {
        margin: auto;
        width: 500px;
        height: 600px;
        border-radius: 10px;
        border: 1px solid #CCC;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .content {
            flex: 1;
        }

        .composer {
            padding: 20px;
        }
    }
</style>