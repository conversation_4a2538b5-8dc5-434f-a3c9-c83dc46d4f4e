import type {LabeledRecord} from "./types";
import {
    MENTION_CLASS,
    MENTION_ID_ATTRIBUTE,
    MENTION_OTHER_PREFIX,
    MENTION_USER_CLASS,
    MENTION_USER_PREFIX,
    MARKDOWN_PATTERNS
} from "./constants";

export function createMentionSpan(mentionedRecord: LabeledRecord, searchType: 'user' | 'other'): HTMLSpanElement {
    const mentionSpan = document.createElement('span');
    const prefix = searchType === 'user' ? MENTION_USER_PREFIX : MENTION_OTHER_PREFIX;
    mentionSpan.textContent = `${prefix}${mentionedRecord.name}`;
    mentionSpan.contentEditable = 'false';
    mentionSpan.className = MENTION_CLASS;

    if (searchType === 'user') {
        mentionSpan.classList.add(MENTION_USER_CLASS);
    }

    mentionSpan.setAttribute(MENTION_ID_ATTRIBUTE, mentionedRecord.id);

    return mentionSpan;
}

export function getCurrentSelectionRange(): Range | null {
    const selection = window.getSelection();
    return selection && selection.rangeCount > 0 ? selection.getRangeAt(0).cloneRange() : null;
}

export function extractTextBeforeCaret(contentEditableDiv: HTMLDivElement, range: Range): string {
    const preCaretRange = range.cloneRange();
    preCaretRange.selectNodeContents(contentEditableDiv);
    preCaretRange.setEnd(range.endContainer, range.endOffset);
    return preCaretRange.toString();
}

export function isInsideMention(range: Range): boolean {
    const node = range.startContainer;
    const parent = node instanceof Element ? node : node.parentElement;
    return parent?.closest(`.${MENTION_CLASS}`) !== null;
}

export function buildMentionRegex(prefix: string): RegExp {
    return new RegExp(`(?:^|\\s)${prefix}([a-zA-Z0-9_]*)$`);
}

export function extractRawText(container: HTMLElement): string {
    let result = '';

    // Custom iterator to skip internal mention label text
    const iterator = document.createNodeIterator(
        container,
        NodeFilter.SHOW_ALL,
        {
            acceptNode(node) {
                if (
                    node.nodeType === Node.TEXT_NODE &&
                    node.parentElement?.classList.contains(MENTION_CLASS)
                ) {
                    return NodeFilter.FILTER_REJECT;
                }
                return NodeFilter.FILTER_ACCEPT;
            }
        }
    );

    // Traverse the DOM tree and extract text
    let currentNode: Node | null;
    while ((currentNode = iterator.nextNode())) {
        if (currentNode.nodeType === Node.ELEMENT_NODE) {
            const el = currentNode as HTMLElement;
            if (el.classList.contains(MENTION_CLASS)) {
                const id = el.getAttribute(MENTION_ID_ATTRIBUTE);
                result += id ? `@${id}` : '';
            }
        } else if (currentNode.nodeType === Node.TEXT_NODE) {
            result += currentNode.textContent ?? '';
        }
    }

    // Remove trailing/leading whitespace
    return result.trim();
}

export function applyMarkdownFormatting(container: HTMLElement): void {
    // Store current selection
    const selection = window.getSelection();
    let range: Range | null = null;
    let caretOffset = 0;
    let caretNode: Node | null = null;

    if (selection && selection.rangeCount > 0) {
        range = selection.getRangeAt(0);
        caretNode = range.startContainer;
        caretOffset = range.startOffset;
    }

    // Get the current HTML content
    const currentHTML = container.innerHTML;

    // Apply markdown formatting to the entire content
    const formattedHTML = formatContentWithMarkdown(currentHTML);

    // Only update if content changed
    if (formattedHTML !== currentHTML) {
        container.innerHTML = formattedHTML;

        // Restore cursor position
        if (caretNode && range) {
            try {
                // Find the equivalent node in the new DOM
                const newRange = document.createRange();
                const textNodes = getAllTextNodes(container);

                let currentOffset = 0;
                let targetNode: Node | null = null;
                let targetOffset = 0;

                for (const textNode of textNodes) {
                    const nodeLength = textNode.textContent?.length || 0;
                    if (currentOffset + nodeLength >= caretOffset) {
                        targetNode = textNode;
                        targetOffset = caretOffset - currentOffset;
                        break;
                    }
                    currentOffset += nodeLength;
                }

                if (targetNode) {
                    newRange.setStart(targetNode, Math.min(targetOffset, targetNode.textContent?.length || 0));
                    newRange.setEnd(targetNode, Math.min(targetOffset, targetNode.textContent?.length || 0));
                    selection.removeAllRanges();
                    selection.addRange(newRange);
                }
            } catch (e) {
                // If cursor restoration fails, just place it at the end
                const newRange = document.createRange();
                newRange.selectNodeContents(container);
                newRange.collapse(false);
                selection?.removeAllRanges();
                selection?.addRange(newRange);
            }
        }
    }
}

function getAllTextNodes(container: HTMLElement): Text[] {
    const textNodes: Text[] = [];
    const walker = document.createTreeWalker(
        container,
        NodeFilter.SHOW_TEXT,
        null
    );

    let node;
    while (node = walker.nextNode()) {
        textNodes.push(node as Text);
    }

    return textNodes;
}

function formatContentWithMarkdown(html: string): string {
    // First, extract mentions and preserve them
    const mentionPlaceholders: string[] = [];
    let processedHTML = html.replace(/<span[^>]*class="[^"]*mention[^"]*"[^>]*>.*?<\/span>/g, (match) => {
        const placeholder = `__MENTION_${mentionPlaceholders.length}__`;
        mentionPlaceholders.push(match);
        return placeholder;
    });

    // Remove existing markdown formatting to avoid double-formatting
    processedHTML = processedHTML.replace(/<(strong|em|del|code|u)[^>]*class="markdown-[^"]*"[^>]*>(.*?)<\/\1>/g, '$2');

    // Apply markdown patterns to text content only
    for (const pattern of MARKDOWN_PATTERNS) {
        // Reset regex lastIndex to ensure proper matching
        pattern.regex.lastIndex = 0;
        processedHTML = processedHTML.replace(pattern.regex, (match, delimiters, content) => {
            // Preserve the original delimiters in the content but wrap with formatting
            return `<${pattern.tag} class="${pattern.class}">${delimiters}${content}${delimiters}</${pattern.tag}>`;
        });
    }

    // Restore mentions
    mentionPlaceholders.forEach((mention, index) => {
        processedHTML = processedHTML.replace(`__MENTION_${index}__`, mention);
    });

    return processedHTML;
}